"""
flow_trainer.py

Ulepszony prototyp "flow-neuronal" transformer z menu terminalowym.
Funkcje dodane w porównaniu do oryginału:
 - automatyczny split train/validation
 - logging perplexity i zapisy state_dict co epokę
 - interaktywny REPL do generowania tekstu (greedy/top-k/top-p)
 - opcja użycia SentencePiece (jeśli zainstalowane) do subword tokenizacji
 - zapisz model w postaci state_dict oraz opcja quantize + save
 - monitoring średnich wartości "flow"
 - prosty early stopping i LR schedule z warmup
 - usuwa optimizer z checkpointu by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> (opcjonalnie zachowuje)

Uwaga: nadal prototyp. Do treningu wielkich modeli wymagane są rozproszone narzędzia.

Wymagania: Python 3.8+, PyTorch. Opcjonalnie: torchvision, torchaudio, sentencepiece, bitsandbytes.
"""

import os
import sys
import time
import math
import json
import glob
import random
from pathlib import Path
from typing import List, Tuple, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split

try:
    from PIL import Image
    import torchvision.transforms as T
except Exception:
    Image = None

try:
    import torchaudio
except Exception:
    torchaudio = None

# Optional tokenization: sentencepiece
USE_SENTENCEPIECE = False
try:
    import sentencepiece as spm
    USE_SENTENCEPIECE = True
except Exception:
    USE_SENTENCEPIECE = False

# Optional quantization (bitsandbytes)
try:
    # import bitsandbytes as bnb  # noqa: F401
    HAVE_BNB = True
except Exception:
    HAVE_BNB = False

# --------------------------- Utilities & Config ---------------------------

DEFAULT_CONFIG = {
    "d_model": 512,
    "n_layers": 6,
    "n_heads": 8,
    "ff_mult": 4,
    "seq_len": 128,
    "batch_size": 32,
    "lr": 3e-4,
    "weight_decay": 0.01,
    "dropout": 0.1,
    "device": "cuda" if torch.cuda.is_available() else "cpu",
    "save_dir": "checkpoints",
    "warmup_steps": 1000,
}

os.makedirs(DEFAULT_CONFIG["save_dir"], exist_ok=True)

SEED = 42
random.seed(SEED)
torch.manual_seed(SEED)

# --------------------------- Tokenizers / Vocab ---------------------------

class Vocab:
    def __init__(self, min_freq=1):
        self.min_freq = min_freq
        self.freq = {}
        self.itos = []
        self.stoi = {}
        self.PAD = "<pad>"
        self.UNK = "<unk>"
        self.BOS = "<bos>"
        self.EOS = "<eos>"

    def build(self, texts: List[str]):
        for t in texts:
            for tok in t.split():
                self.freq[tok] = self.freq.get(tok, 0) + 1
        toks = [t for t,c in self.freq.items() if c >= self.min_freq]
        toks = [self.PAD, self.UNK, self.BOS, self.EOS] + sorted(toks)
        self.itos = toks
        self.stoi = {t:i for i,t in enumerate(self.itos)}

    def encode(self, text: str) -> List[int]:
        tokens = text.split()
        ids = [self.stoi.get(t, self.stoi[self.UNK]) for t in tokens]
        return ids

    def decode(self, ids: List[int]) -> str:
        toks = [self.itos[i] if i < len(self.itos) else self.UNK for i in ids]
        return " ".join(toks)

    def __len__(self):
        return len(self.itos)

class SentencePieceWrapper:
    def __init__(self, model_prefix: str, vocab_size: int = 32000):
        self.model_prefix = model_prefix
        self.vocab_size = vocab_size
        self.sp = None

    def train(self, corpus_paths: List[str]):
        # merge corpus_paths into single file
        joined = self.model_prefix + '.corpus.txt'
        with open(joined, 'w', encoding='utf-8') as out:
            for p in corpus_paths:
                with open(p, 'r', encoding='utf-8') as fh:
                    out.write(fh.read())
                    out.write('')
        spm.SentencePieceTrainer.train(input=joined, model_prefix=self.model_prefix, vocab_size=self.vocab_size, model_type='unigram')
        self.sp = spm.SentencePieceProcessor()
        self.sp.load(self.model_prefix + '.model')
        os.remove(joined)

    def encode(self, text: str) -> List[int]:
        return self.sp.encode(text, out_type=int)

    def decode(self, ids: List[int]) -> str:
        return self.sp.decode(ids)

# --------------------------- Datasets -----------------------------------

class TextFolderDataset(Dataset):
    def __init__(self, folder: str, seq_len: int, tokenizer: Optional[object]=None, use_sp=False):
        files = glob.glob(os.path.join(folder, "**/*.*"), recursive=True)
        texts = []
        for f in files:
            if os.path.isfile(f):
                try:
                    with open(f, "r", encoding="utf-8") as fh:
                        texts.append(fh.read())
                except Exception:
                    continue
        if len(texts) == 0:
            raise ValueError("No text files found in folder")
        self.seq_len = seq_len
        self.texts = texts
        self.tokenizer = tokenizer
        self.use_sp = use_sp
        if use_sp:
            assert hasattr(tokenizer, 'encode') and hasattr(tokenizer, 'decode')
        else:
            # legacy Vocab
            if tokenizer is None:
                self.vocab = Vocab()
                self.vocab.build(texts)
                self.tokenizer = self.vocab
            else:
                self.vocab = tokenizer
        self.data = self._build_sequences()

    def _build_sequences(self):
        seqs = []
        for t in self.texts:
            if self.use_sp:
                ids = [self.tokenizer.sp.PieceToId('<s>')] if hasattr(self.tokenizer.sp, 'PieceToId') else self.tokenizer.encode(t)
                # SentencePiece handles BOS/EOS differently; we will not add explicit tokens here
                ids = self.tokenizer.encode(t)
            else:
                ids = [self.tokenizer.stoi[self.tokenizer.BOS]] + self.tokenizer.encode(t) + [self.tokenizer.stoi[self.tokenizer.EOS]]
            # chunk
            for i in range(0, max(1, len(ids)-1), self.seq_len):
                chunk = ids[i:i+self.seq_len]
                if len(chunk) < 2:
                    continue
                if len(chunk) < self.seq_len:
                    pad_id = self.tokenizer.stoi[self.tokenizer.PAD] if not self.use_sp else 0
                    chunk = chunk + [pad_id]*(self.seq_len-len(chunk))
                seqs.append(chunk)
        return seqs

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        seq = torch.tensor(self.data[idx], dtype=torch.long)
        x = seq[:-1]
        y = seq[1:]
        return x, y

class ImageFolderPatchDataset(Dataset):
    def __init__(self, folder: str, image_size: int=128, patch_size: int=16):
        if Image is None:
            raise RuntimeError("PIL and torchvision required for image dataset")
        files = glob.glob(os.path.join(folder, "**/*.*"), recursive=True)
        imgs = [f for f in files if f.lower().endswith((".png",".jpg",".jpeg",".bmp"))]
        if len(imgs) == 0:
            raise ValueError("No image files found in folder")
        self.imgs = imgs
        self.image_size = image_size
        self.patch_size = patch_size
        self.transform = T.Compose([T.Resize((image_size,image_size)), T.ToTensor()])

    def __len__(self):
        return len(self.imgs)

    def __getitem__(self, idx):
        p = self.imgs[idx]
        img = Image.open(p).convert("RGB")
        t = self.transform(img)  # C,H,W
        # convert to patches
        C,H,W = t.shape
        ph = self.patch_size
        assert H%ph==0 and W%ph==0, "Image dimensions must be divisible by patch_size"
        patches = t.unfold(1, ph, ph).unfold(2, ph, ph)
        patches = patches.contiguous().view(C, -1, ph, ph)
        patches = patches.permute(1,0,2,3).contiguous().view(patches.size(1), -1)  # Npatches x (C*ph*ph)
        return patches  # return sequence of patch vectors

# --------------------------- Model components ---------------------------

class FlowGenerator(nn.Module):
    def __init__(self, d_model, n_heads, hidden=128):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(d_model, hidden),
            nn.ReLU(),
            nn.Linear(hidden, n_heads),
            nn.Sigmoid()
        )

    def forward(self, x):
        B,S,D = x.shape
        out = self.net(x.view(B*S, D))
        out = out.view(B, S, -1)
        return out

class FlowMultiHeadAttention(nn.Module):
    def __init__(self, d_model, n_heads, dropout=0.1):
        super().__init__()
        assert d_model % n_heads == 0
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_head = d_model // n_heads
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_head)
        self.flow_gen = FlowGenerator(d_model, n_heads)

    def forward(self, x, mask=None):
        B,S,D = x.shape
        q = self.W_q(x).view(B,S,self.n_heads,self.d_head).transpose(1,2)
        k = self.W_k(x).view(B,S,self.n_heads,self.d_head).transpose(1,2)
        v = self.W_v(x).view(B,S,self.n_heads,self.d_head).transpose(1,2)
        flow = self.flow_gen(x)
        flow = flow.permute(0,2,1).unsqueeze(-1)
        q = q * flow
        v = v * flow
        scores = torch.matmul(q, k.transpose(-2,-1)) / self.scale
        if mask is not None:
            scores = scores.masked_fill(mask==0, -1e9)
        attn = torch.softmax(scores, dim=-1)
        attn = self.dropout(attn)
        out = torch.matmul(attn, v)
        out = out.transpose(1,2).contiguous().view(B,S,D)
        out = self.W_o(out)
        return out

class FlowFFN(nn.Module):
    def __init__(self, d_model, ff_mult=4, dropout=0.1):
        super().__init__()
        d_hidden = d_model * ff_mult
        self.W1 = nn.Linear(d_model, d_hidden)
        self.W2 = nn.Linear(d_hidden, d_model)
        self.act = nn.GELU()
        self.dropout = nn.Dropout(dropout)
        self.flow_gen = FlowGenerator(d_model, 1)

    def forward(self, x):
        flow = self.flow_gen(x).squeeze(-1).unsqueeze(-1)
        h = self.W1(x)
        h = self.act(h) * flow
        h = self.dropout(h)
        h = self.W2(h)
        return h

class TransformerBlock(nn.Module):
    def __init__(self, d_model, n_heads, ff_mult=4, dropout=0.1):
        super().__init__()
        self.ln1 = nn.LayerNorm(d_model)
        self.attn = FlowMultiHeadAttention(d_model, n_heads, dropout)
        self.ln2 = nn.LayerNorm(d_model)
        self.ffn = FlowFFN(d_model, ff_mult, dropout)

    def forward(self, x, mask=None):
        x = x + self.attn(self.ln1(x), mask)
        x = x + self.ffn(self.ln2(x))
        return x

class FlowTransformer(nn.Module):
    def __init__(self, cfg, vocab_size=None, patch_dim=None):
        super().__init__()
        d = cfg['d_model']
        self.d = d
        self.vocab_size = vocab_size
        self.patch_dim = patch_dim
        if vocab_size is not None:
            self.token_emb = nn.Embedding(vocab_size, d)
        else:
            self.token_emb = None
        if patch_dim is not None:
            self.patch_proj = nn.Linear(patch_dim, d)
        else:
            self.patch_proj = None
        self.pos_emb = nn.Parameter(torch.randn(1, cfg['seq_len'], d))
        self.layers = nn.ModuleList([TransformerBlock(d, cfg['n_heads'], cfg['ff_mult'], cfg['dropout']) for _ in range(cfg['n_layers'])])
        self.ln_f = nn.LayerNorm(d)
        if vocab_size is not None:
            self.head = nn.Linear(d, vocab_size, bias=False)
        else:
            self.head = None

    def forward(self, x, mask=None, modality='text'):
        if modality == 'text':
            assert self.token_emb is not None
            h = self.token_emb(x)
        else:
            assert self.patch_proj is not None
            h = self.patch_proj(x)
        h = h + self.pos_emb[:, :h.size(1), :]
        for layer in self.layers:
            h = layer(h, mask)
        h = self.ln_f(h)
        if self.head is not None:
            return self.head(h)
        return h

# --------------------------- Trainer ------------------------------------

class Trainer:
    def __init__(self, cfg):
        self.cfg = cfg
        self.device = torch.device(cfg.get('device','cpu'))
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.train_loader = None
        self.valid_loader = None
        self.vocab = None
        self.modality = 'text'
        self.use_sp = False
        self.sp_wrapper = None
        self.best_val = float('inf')
        self.early_stop_count = 0

    def build_model_for_text(self, vocab_size:int):
        self.vocab = vocab_size
        self.model = FlowTransformer(self.cfg, vocab_size=vocab_size)
        self.model.to(self.device)
        self._build_opt()

    def build_model_for_patches(self, patch_dim:int):
        self.model = FlowTransformer(self.cfg, vocab_size=None, patch_dim=patch_dim)
        self.model.to(self.device)
        self._build_opt()

    def _build_opt(self):
        self.optimizer = torch.optim.AdamW(self.model.parameters(), lr=self.cfg['lr'], weight_decay=self.cfg['weight_decay'])
        # simple scheduler with warmup
        def lr_lambda(step):
            if step < self.cfg.get('warmup_steps', 1000):
                return float(step) / float(max(1, self.cfg.get('warmup_steps',1000)))
            return 0.5 * (1.0 + math.cos(math.pi * (step - self.cfg.get('warmup_steps',1000)) / 100000.0))
        self.scheduler = torch.optim.lr_scheduler.LambdaLR(self.optimizer, lr_lambda)

    def save_state_dict(self, path: str, save_optimizer: bool=False):
        state = {
            'cfg': self.cfg,
            'model_state': self.model.state_dict(),
            'vocab': self.vocab,
            'modality': self.modality,
        }
        if save_optimizer and self.optimizer is not None:
            state['optimizer_state'] = self.optimizer.state_dict()
        torch.save(state, path)
        print(f"Saved state_dict to {path}")

    def load_state_dict(self, path: str):
        st = torch.load(path, map_location=self.device)
        cfg = st.get('cfg', None)
        if cfg is not None:
            self.cfg = cfg
        self.modality = st.get('modality','text')
        vocab = st.get('vocab', None)
        if vocab is not None:
            self.build_model_for_text(vocab)
        else:
            if self.model is None:
                raise RuntimeError('Model not initialized for patch modality. Build model first.')
        self.model.load_state_dict(st['model_state'])
        print(f"Loaded state_dict from {path}")

    def train_epoch(self, epoch:int, log_interval=50):
        self.model.train()
        total_loss = 0.0
        it = 0
        for batch_idx, batch in enumerate(self.train_loader):
            it += 1
            if self.modality == 'text':
                x,y = batch
                x = x.to(self.device)
                y = y.to(self.device)
                logits = self.model(x)
                loss = F.cross_entropy(logits.view(-1, logits.size(-1)), y.view(-1), ignore_index=0)
            else:
                patches = batch
                if isinstance(patches, list) or patches.dim()==3:
                    patches = patches.to(self.device)
                logits = self.model(patches, modality='patch')
                loss = F.mse_loss(logits, patches)

            self.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            if self.scheduler is not None:
                self.scheduler.step()
            total_loss += loss.item()
            if batch_idx % log_interval == 0:
                print(f"Epoch {epoch} batch {batch_idx} loss {loss.item():.4f}")
        avg = total_loss / max(1, it)
        print(f"Epoch {epoch} finished. Avg loss {avg:.4f}")
        return avg

    def evaluate(self):
        self.model.eval()
        total_loss = 0.0
        it = 0
        with torch.no_grad():
            for batch in self.valid_loader:
                it += 1
                if self.modality == 'text':
                    x,y = batch
                    x = x.to(self.device)
                    y = y.to(self.device)
                    logits = self.model(x)
                    loss = F.cross_entropy(logits.view(-1, logits.size(-1)), y.view(-1), ignore_index=0)
                else:
                    patches = batch
                    if isinstance(patches, list) or patches.dim()==3:
                        patches = patches.to(self.device)
                    logits = self.model(patches, modality='patch')
                    loss = F.mse_loss(logits, patches)
                total_loss += loss.item()
        avg = total_loss / max(1, it)
        ppl = math.exp(avg) if avg < 20 else float('inf')
        print(f"Validation loss: {avg:.4f} PPL: {ppl:.2f}")
        return avg, ppl

    def log_flow_stats(self, n_samples=1):
        flows = []
        for m in self.model.modules():
            if isinstance(m, FlowGenerator):
                try:
                    batch = next(iter(self.train_loader))
                except StopIteration:
                    return
                x = batch[0].to(self.device) if self.modality=='text' else batch.to(self.device)
                with torch.no_grad():
                    f = m(x).mean().item()
                    flows.append(f)
        print("Flow means per FlowGenerator:", flows)

# --------------------------- Helpers: generation, metrics -----------------

def generate_text(trainer, prompt, max_len=128, top_k=50, top_p=0.0, temperature=1.0):
    model = trainer.model.eval().to(trainer.device)
    vocab = trainer.train_loader.dataset.tokenizer if not trainer.use_sp else trainer.sp_wrapper
    if trainer.use_sp:
        ids = vocab.encode(prompt)
    else:
        ids = [vocab.stoi.get(t, vocab.stoi[vocab.UNK]) for t in prompt.split()]
    for _ in range(max_len):
        x = torch.tensor([ids[-trainer.cfg['seq_len']:]], dtype=torch.long).to(trainer.device)
        with torch.no_grad():
            logits = model(x)[:, -1, :] / max(1e-8, temperature)
            probs = torch.softmax(logits, dim=-1).squeeze(0)
            if top_p>0.0:
                sorted_probs, sorted_idx = torch.sort(probs, descending=True)
                cum = torch.cumsum(sorted_probs, dim=0)
                mask = cum > top_p
                sorted_probs[mask] = 0
                if sorted_probs.sum().item() == 0:
                    sorted_probs = probs
                    sorted_idx = torch.arange(len(probs))
                probs_filtered = sorted_probs / sorted_probs.sum()
                choice = int(torch.multinomial(probs_filtered, 1).item())
                next_id = int(sorted_idx[choice].item())
            elif top_k>0:
                vals, idx = torch.topk(probs, top_k)
                probs_k = vals / vals.sum()
                next_id = int(idx[torch.multinomial(probs_k,1)].item())
            else:
                next_id = int(torch.multinomial(probs,1).item())
        ids.append(next_id)
        if not trainer.use_sp and next_id == vocab.stoi[vocab.EOS]:
            break
        if trainer.use_sp and hasattr(vocab, 'sp') and next_id == vocab.sp.PieceToId('</s>'):
            break
    if trainer.use_sp:
        return vocab.decode(ids)
    return vocab.decode(ids)

def greedy_generate(trainer, prompt_tokens, max_len=100):
    model = trainer.model.eval().to(trainer.device)
    ids = prompt_tokens[:]
    seq_len = trainer.cfg['seq_len']
    for _ in range(max_len):
        input_ids = ids[-seq_len:] if len(ids) >= seq_len else ids
        x = torch.tensor([input_ids], dtype=torch.long).to(trainer.device)
        # Jeśli model wymaga maski, dodaj ją tutaj:
        # mask = generate_causal_mask(x.size(1), trainer.device)
        # logits = model(x, mask=mask)[:, -1, :].softmax(-1)
        logits = model(x)[:, -1, :].softmax(-1)
        next_id = int(logits.argmax(-1).cpu().numpy())
        ids.append(next_id)
        if next_id == trainer.vocab.stoi.get(trainer.vocab.EOS, None):
            break
    return trainer.vocab.decode(ids)

def diversity_metrics(sentences):
    from collections import Counter
    unigrams = Counter()
    bigrams = Counter()
    total_unigrams = 0
    for s in sentences:
        toks = s.split()
        total_unigrams += len(toks)
        for i,t in enumerate(toks):
            unigrams[t]+=1
            if i>0:
                bigrams[(toks[i-1],toks[i])]+=1
    d1 = len(unigrams)/max(1,total_unigrams)
    d2 = len(bigrams)/max(1,total_unigrams)
    return {'distinct-1':d1, 'distinct-2':d2}

# --------------------------- Command-line menu ---------------------------

def print_menu():
    print("FLOW-TRAINER - MENU")
    print("1. Wczytaj dane z folderu")
    print("2. Trenować epoki")
    print("3. Zapisz model (state_dict)")
    print("4. Wczytaj model (state_dict)")
    print("5. Testuj model (eval)")
    print("6. Zakończ")
    print("7. Interaktywny REPL (generate)")
    print("8. Quantize i zapisz (dynamic) [opcjonalnie]")


def main():
    cfg = DEFAULT_CONFIG.copy()
    trainer = Trainer(cfg)
    data_loaded = False
    dataset = None
    while True:
        print_menu()
        choice = input("Wybierz opcję (1-8): ").strip()
        if choice == '1':
            folder = input("Ścieżka do folderu danych: ").strip()
            modality = input("Modalność (text/image/audio) [text]: ").strip() or 'text'
            seq_len = int(input(f"Długość sekwencji [{cfg['seq_len']}]: ").strip() or cfg['seq_len'])
            cfg['seq_len'] = seq_len
            cfg['batch_size'] = int(input(f"Batch size [{cfg['batch_size']}]: ").strip() or cfg['batch_size'])
            trainer.cfg = cfg
            trainer.modality = modality
            print("Wczytywanie danych...")
            if modality == 'text':
                use_sp = 'y' == input(f"Użyć SentencePiece jeśli dostępne? (y/n) [n]: ").strip().lower()
                trainer.use_sp = use_sp and USE_SENTENCEPIECE
                if use_sp and not USE_SENTENCEPIECE:
                    print("sentencepiece nie jest zainstalowane. Kontynuuję bez SP.")
                    trainer.use_sp = False
                if trainer.use_sp:
                    vocab_size = int(input("Rozmiar słownika SentencePiece [32000]: ").strip() or 32000)
                    sp_prefix = input("Prefix modelu SP [spm_model]: ").strip() or 'spm_model'
                    spw = SentencePieceWrapper(sp_prefix, vocab_size=vocab_size)
                    # train sp on files in folder (may be slow)
                    try:
                        files = glob.glob(os.path.join(folder, "**/*.*"), recursive=True)
                        text_files = [f for f in files if os.path.isfile(f)]
                        spw.train(text_files)
                        trainer.sp_wrapper = spw
                        ds = TextFolderDataset(folder, seq_len, tokenizer=spw, use_sp=True)
                        trainer.build_model_for_text(vocab_size)
                        trainer.train_loader = DataLoader(ds, batch_size=cfg['batch_size'], shuffle=True, drop_last=True, collate_fn=lambda x: default_collate_text(x))
                        trainer.valid_loader = DataLoader(ds, batch_size=cfg['batch_size'], shuffle=False, drop_last=False, collate_fn=lambda x: default_collate_text(x))
                        print(f"Text dataset loaded with SP. Vocab size {vocab_size} sequences {len(ds)}")
                        trainer.train_loader.dataset = ds
                        data_loaded = True
                    except Exception as e:
                        print("Błąd trenowania SentencePiece:", e)
                        continue
                else:
                    vocab = Vocab()
                    ds = TextFolderDataset(folder, seq_len, vocab)
                    # split train/val
                    n = len(ds)
                    n_val = max(1, int(0.1*n))
                    n_train = n - n_val
                    train_ds, val_ds = random_split(ds, [n_train, n_val])
                    trainer.build_model_for_text(len(ds.tokenizer))
                    trainer.train_loader = DataLoader(train_ds, batch_size=cfg['batch_size'], shuffle=True, drop_last=True, collate_fn=lambda x: default_collate_text(x))
                    trainer.valid_loader = DataLoader(val_ds, batch_size=cfg['batch_size'], shuffle=False, drop_last=False, collate_fn=lambda x: default_collate_text(x))
                    trainer.train_loader.dataset.tokenizer = ds.tokenizer
                    trainer.valid_loader.dataset.tokenizer = ds.tokenizer
                    print(f"Text dataset loaded. Vocab size {len(ds.tokenizer)} sequences total {len(ds)} train {len(train_ds)} val {len(val_ds)}")
                    data_loaded = True
            elif modality == 'image':
                img_size = int(input("Image size [128]: ").strip() or 128)
                patch = int(input("Patch size [16]: ").strip() or 16)
                ds = ImageFolderPatchDataset(folder, image_size=img_size, patch_size=patch)
                collate = lambda batch: default_collate_patches(batch)
                trainer.build_model_for_patches(ds[0].shape[-1])
                # split
                n = len(ds)
                n_val = max(1, int(0.1*n))
                n_train = n - n_val
                train_ds, val_ds = random_split(ds, [n_train, n_val])
                trainer.train_loader = DataLoader(train_ds, batch_size=cfg['batch_size'], shuffle=True, collate_fn=collate)
                trainer.valid_loader = DataLoader(val_ds, batch_size=cfg['batch_size'], shuffle=False, collate_fn=collate)
                print(f"Image dataset loaded. images {len(ds)} patches per example {ds[0].shape[0]}")
                data_loaded = True
            else:
                print("Audio modality currently experimental.")
                if torchaudio is None:
                    print("torchaudio not installed. Install it to use audio modality.")
                    continue
        elif choice == '2':
            if not data_loaded:
                print("Najpierw wczytaj dane (opcja 1)")
                continue
            epochs = int(input("Liczba epok [1]: ").strip() or 1)
            checkpoint_every = int(input("Checkpoint co ile epok [1]: ").strip() or 1)
            keep_optimizer = 'y' == input("Zachować optimizer w checkpoint? (zwiększa rozmiar) (y/n) [n]: ").strip().lower()
            early_stop_patience = int(input("EarlyStop patience [3]: ").strip() or 3)
            for e in range(1, epochs+1):
                trainer.train_epoch(e)
                val_loss, val_ppl = trainer.evaluate()
                trainer.log_flow_stats()
                # checkpoint
                if e % checkpoint_every == 0:
                    path = os.path.join(cfg['save_dir'], f'ckpt_epoch{e}.pt')
                    trainer.save_state_dict(path, save_optimizer=keep_optimizer)
                # early stopping
                if val_loss < trainer.best_val:
                    trainer.best_val = val_loss
                    trainer.early_stop_count = 0
                else:
                    trainer.early_stop_count += 1
                if trainer.early_stop_count >= early_stop_patience:
                    print("Early stopping triggered.")
                    break
        elif choice == '3':
            path = input(f"Ścieżka zapisu [{cfg['save_dir']}/model_state.pth]: ").strip() or os.path.join(cfg['save_dir'],'model_state.pth')
            trainer.save_state_dict(path, save_optimizer=False)
        elif choice == '4':
            path = input("Ścieżka do state_dict: ").strip()
            if not os.path.exists(path):
                print("Plik nie istnieje")
                continue
            trainer.load_state_dict(path)
            data_loaded = True
        elif choice == '5':
            if trainer.valid_loader is None:
                print("Brak danych testowych. Wczytaj dane najpierw.")
                continue
            trainer.evaluate()
        elif choice == '6':
            print("Zakończono.")
            break
        elif choice == '7':
            if trainer.model is None:
                print("Brak modelu. Wczytaj lub wytrenuj model najpierw.")
                continue
            print("Wejdz do trybu interakcji. 'exit' aby wyjsc.")
            while True:
                prompt = input("PROMPT >>> ").strip()
                if prompt.lower() in ('exit','quit'):
                    break
                out = generate_text(trainer, prompt, max_len=trainer.cfg['seq_len'], top_k=40, temperature=0.8)
                print(out)
        elif choice == '8':
            if trainer.model is None:
                print("Brak modelu.")
                continue
            path = input(f"Ścieżka zapisu skwantowanego modelu [{cfg['save_dir']}/model_q.pth]: ").strip() or os.path.join(cfg['save_dir'],'model_q.pth')
            print("Wykonuję dynamiczną kwantyzację... (torch.quantization.quantize_dynamic)")
            qmodel = torch.quantization.quantize_dynamic(trainer.model, {nn.Linear}, dtype=torch.qint8)
            torch.save(qmodel.state_dict(), path)
            print(f"Zapisano skwantowany state_dict do {path}")
        else:
            print("Nieprawidłowy wybór")

# --------------------------- Collate helpers -----------------------------

def default_collate_text(batch):
    xs = torch.stack([b[0] for b in batch], dim=0)
    ys = torch.stack([b[1] for b in batch], dim=0)
    return xs, ys

def default_collate_patches(batch):
    S = [b.shape[0] for b in batch]
    maxS = max(S)
    dim = batch[0].shape[1]
    out = torch.zeros(len(batch), maxS, dim)
    for i,b in enumerate(batch):
        out[i,:b.shape[0],:] = b
    return out

# --------------------------- Entry point --------------------------------

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print('Przerwano przez użytkownika')
        sys.exit(0)
