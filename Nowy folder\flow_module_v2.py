"""
Flow Neural Network V2 - Zoptymalizowane Arcydzieło
Kluczowe ulepszenia:
1. Hierarchiczny Flow Control
2. Adaptive Sparsity Learning  
3. Memory-Efficient Flow Routing
4. Multi-Scale Flow Patterns
5. Dynamic Flow Topology
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, List, Union
import math

class AdaptiveFlowRouter(nn.Module):
    """
    Inteligentny router przepływu - uczy się optymalnych wzorców połączeń
    Zamiast generować pełne macierze, tworzy kompaktowe reprezentacje
    """
    
    def __init__(self, input_dim: int, output_dim: int, 
                 num_flow_patterns: int = 16, base_sparsity: float = 0.1):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_patterns = num_flow_patterns
        self.base_sparsity = base_sparsity
        
        # Biblioteka wzorców przepływu (uczonych)
        self.flow_patterns = nn.Parameter(
            torch.randn(num_flow_patterns, output_dim, input_dim) * 0.1
        )
        
        # Selektor wzorców - który wzorzec użyć dla danego wejścia
        self.pattern_selector = nn.Sequential(
            nn.Linear(input_dim, input_dim // 4),
            nn.GELU(),
            nn.Linear(input_dim // 4, num_flow_patterns),
            nn.Softmax(dim=-1)
        )
        
        # Modulator intensywności przepływu
        self.flow_intensity = nn.Sequential(
            nn.Linear(input_dim, input_dim // 8),
            nn.GELU(), 
            nn.Linear(input_dim // 8, 1),
            nn.Sigmoid()
        )
        
        # Adaptive sparsity controller
        self.sparsity_controller = nn.Parameter(torch.tensor(base_sparsity))
        self.sparsity_adaptation = nn.Sequential(
            nn.Linear(input_dim, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """
        Args:
            x: (batch_size, seq_len, input_dim)
        Returns:
            flow_matrix: (batch_size, seq_len, output_dim, input_dim)
            routing_weights: (batch_size, seq_len, num_patterns)
            metrics: dict
        """
        batch_size, seq_len, _ = x.shape
        
        # 1. Wybierz wzorce przepływu dla każdego tokena
        pattern_weights = self.pattern_selector(x)  # (B, S, num_patterns)
        
        # 2. Skomponuj macierz przepływu jako kombinację wzorców
        flow_matrix = torch.einsum('bsp,pij->bsij', pattern_weights, self.flow_patterns)
        
        # 3. Moduluj intensywność
        intensity = self.flow_intensity(x).unsqueeze(-1)  # (B, S, 1, 1)
        flow_matrix = flow_matrix * intensity
        
        # 4. Adaptive sparsity - każdy token może mieć różną sparsity
        adaptive_sparsity = self.sparsity_controller * self.sparsity_adaptation(x).squeeze(-1)
        
        # 5. Zastosuj sparsity przez top-k z adaptive threshold
        flow_matrix = self._apply_adaptive_sparsity(flow_matrix, adaptive_sparsity)
        
        metrics = {
            'pattern_entropy': -(pattern_weights * torch.log(pattern_weights + 1e-8)).sum(-1).mean(),
            'avg_sparsity': adaptive_sparsity.mean(),
            'flow_intensity': intensity.mean(),
            'pattern_diversity': torch.std(pattern_weights.mean(dim=(0,1)))
        }
        
        return flow_matrix, pattern_weights, metrics
    
    def _apply_adaptive_sparsity(self, flow_matrix: torch.Tensor,
                                sparsity_ratios: torch.Tensor) -> torch.Tensor:
        """Vectorized adaptive sparsity - ZNACZNIE szybsza implementacja"""
        batch_size, seq_len, out_dim, in_dim = flow_matrix.shape

        # Flatten dla vectorized operations
        flat_flow = flow_matrix.view(batch_size, seq_len, -1)
        total_connections = out_dim * in_dim

        # Oblicz k dla każdego tokena
        k_values = (sparsity_ratios * total_connections).long().clamp(1, total_connections)

        # Vectorized top-k przez sorting i masking
        sorted_flows, sort_indices = torch.sort(flat_flow.abs(), dim=-1, descending=True)

        # Stwórz position mask
        positions = torch.arange(total_connections, device=flat_flow.device).expand_as(flat_flow)
        k_expanded = k_values.unsqueeze(-1).expand(-1, -1, total_connections)
        keep_mask = positions < k_expanded

        # Zastosuj mask przez gather/scatter
        sparse_flow = torch.zeros_like(flat_flow)

        # Użyj original values (ze znakiem) dla wybranych pozycji
        original_sorted = torch.gather(flat_flow, -1, sort_indices)
        masked_values = original_sorted * keep_mask.float()
        sparse_flow.scatter_(-1, sort_indices, masked_values)

        return sparse_flow.view(batch_size, seq_len, out_dim, in_dim)

class HierarchicalFlowMatrix(nn.Module):
    """
    Hierarchiczna macierz przepływu - operuje na różnych skalach
    Małe wzorce -> średnie wzorce -> globalne wzorce
    """
    
    def __init__(self, input_dim: int, output_dim: int, num_scales: int = 3):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_scales = num_scales
        
        # Różne skale przepływu
        self.flow_scales = nn.ModuleList()
        
        for scale in range(num_scales):
            # Każda skala ma inną liczbę wzorców i sparsity
            num_patterns = 8 * (2 ** scale)  # 8, 16, 32 wzorców
            sparsity = 0.05 * (scale + 1)    # 0.05, 0.1, 0.15 sparsity
            
            self.flow_scales.append(
                AdaptiveFlowRouter(input_dim, output_dim, num_patterns, sparsity)
            )
        
        # Kombinator skal - decyduje jak połączyć różne skale
        self.scale_combiner = nn.Sequential(
            nn.Linear(input_dim, num_scales),
            nn.Softmax(dim=-1)
        )
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, Dict]:
        batch_size, seq_len, _ = x.shape
        
        # Wagi dla różnych skal
        scale_weights = self.scale_combiner(x)  # (B, S, num_scales)
        
        # Zbierz przepływy z wszystkich skal
        all_flows = []
        all_metrics = {}
        
        for i, flow_scale in enumerate(self.flow_scales):
            flow_matrix, patterns, metrics = flow_scale(x)
            all_flows.append(flow_matrix)
            
            # Dodaj metryki z prefiksem skali
            for key, value in metrics.items():
                all_metrics[f'scale_{i}_{key}'] = value
        
        # Kombinuj przepływy z różnych skal
        stacked_flows = torch.stack(all_flows, dim=-1)  # (B, S, out, in, scales)
        scale_weights_expanded = scale_weights.view(batch_size, seq_len, 1, 1, self.num_scales)
        
        combined_flow = (stacked_flows * scale_weights_expanded).sum(dim=-1)
        
        # Dodaj metryki kombinacji
        all_metrics['scale_entropy'] = -(scale_weights * torch.log(scale_weights + 1e-8)).sum(-1).mean()
        all_metrics['scale_balance'] = torch.std(scale_weights.mean(dim=(0,1)))
        
        return combined_flow, all_metrics

class MemoryEfficientFlowLayer(nn.Module):
    """
    Memory-efficient warstwa Flow z optymalizacjami:
    1. Chunked processing dla dużych sekwencji
    2. Gradient checkpointing
    3. Mixed precision support
    """
    
    def __init__(self, input_dim: int, output_dim: int, 
                 chunk_size: int = 64, use_checkpointing: bool = True):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.chunk_size = chunk_size
        self.use_checkpointing = use_checkpointing
        
        # Hierarchiczny flow
        self.flow_matrix = HierarchicalFlowMatrix(input_dim, output_dim)
        
        # Bias i normalizacja
        self.bias = nn.Parameter(torch.zeros(output_dim))
        self.layer_norm = nn.LayerNorm(output_dim)
        
        # Adaptive activation - sieć sama wybiera aktywację
        self.activation_weights = nn.Parameter(torch.ones(3))  # GELU, ReLU, Swish
        
    def _chunk_forward(self, x_chunk: torch.Tensor, flow_chunk: torch.Tensor) -> torch.Tensor:
        """Forward pass dla jednego chunka"""
        return torch.einsum('bsij,bsj->bsi', flow_chunk, x_chunk)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, Dict]:
        batch_size, seq_len, input_dim = x.shape
        
        # Generuj macierz przepływu
        flow_matrix, flow_metrics = self.flow_matrix(x)
        
        # Chunked processing dla długich sekwencji
        if seq_len > self.chunk_size:
            outputs = []
            
            for start_idx in range(0, seq_len, self.chunk_size):
                end_idx = min(start_idx + self.chunk_size, seq_len)
                
                x_chunk = x[:, start_idx:end_idx]
                flow_chunk = flow_matrix[:, start_idx:end_idx]
                
                if self.use_checkpointing and self.training:
                    chunk_output = torch.utils.checkpoint.checkpoint(
                        self._chunk_forward, x_chunk, flow_chunk
                    )
                else:
                    chunk_output = self._chunk_forward(x_chunk, flow_chunk)
                
                outputs.append(chunk_output)
            
            output = torch.cat(outputs, dim=1)
        else:
            output = torch.einsum('bsij,bsj->bsi', flow_matrix, x)
        
        # Bias
        output = output + self.bias
        
        # Normalizacja
        output = self.layer_norm(output)
        
        # Adaptive activation - kombinacja trzech aktywacji
        activation_probs = F.softmax(self.activation_weights, dim=0)
        
        gelu_out = F.gelu(output)
        relu_out = F.relu(output)
        swish_out = output * torch.sigmoid(output)
        
        output = (activation_probs[0] * gelu_out + 
                 activation_probs[1] * relu_out + 
                 activation_probs[2] * swish_out)
        
        # Dodaj metryki aktywacji
        flow_metrics['activation_gelu_weight'] = activation_probs[0]
        flow_metrics['activation_relu_weight'] = activation_probs[1] 
        flow_metrics['activation_swish_weight'] = activation_probs[2]
        
        return output, flow_metrics

class DynamicFlowTopology(nn.Module):
    """
    Dynamiczna topologia - sieć może zmieniać swoją strukturę w trakcie działania
    """
    
    def __init__(self, d_model: int, max_layers: int = 12, min_layers: int = 3):
        super().__init__()
        self.d_model = d_model
        self.max_layers = max_layers
        self.min_layers = min_layers
        
        # Wszystkie możliwe warstwy
        self.all_layers = nn.ModuleList([
            MemoryEfficientFlowLayer(d_model, d_model)
            for _ in range(max_layers)
        ])
        
        # Router warstw - decyduje które warstwy aktywować
        self.layer_router = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.GELU(),
            nn.Linear(d_model // 4, max_layers),
            nn.Sigmoid()
        )
        
        # Threshold dla aktywacji warstw
        self.activation_threshold = nn.Parameter(torch.tensor(0.5))
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, List[Dict]]:
        # Decyzja o aktywacji warstw na podstawie średniego wejścia
        x_mean = x.mean(dim=(0, 1))  # Średnia po batch i sequence
        layer_probs = self.layer_router(x_mean)  # (max_layers,)
        
        # Aktywuj warstwy powyżej threshold
        active_layers = layer_probs > self.activation_threshold
        
        # Zapewnij minimum warstw
        if active_layers.sum() < self.min_layers:
            top_k_indices = torch.topk(layer_probs, self.min_layers)[1]
            active_layers = torch.zeros_like(layer_probs, dtype=torch.bool)
            active_layers[top_k_indices] = True
        
        # Przepływ przez aktywne warstwy
        all_metrics = []
        
        for i, (layer, is_active) in enumerate(zip(self.all_layers, active_layers)):
            if is_active:
                x, metrics = layer(x)
                metrics['layer_index'] = i
                metrics['layer_probability'] = layer_probs[i]
                all_metrics.append(metrics)
        
        # Dodaj informacje o topologii
        topology_metrics = {
            'active_layers': active_layers.sum().item(),
            'layer_efficiency': active_layers.sum().item() / self.max_layers,
            'avg_layer_prob': layer_probs.mean().item()
        }
        
        all_metrics.append(topology_metrics)
        
        return x, all_metrics

class FlowNetworkV2(nn.Module):
    """
    Ulepszona sieć Flow - Arcydzieło z wszystkimi optymalizacjami
    """

    def __init__(self, vocab_size: int, d_model: int = 512, max_layers: int = 8,
                 min_layers: int = 3, max_seq_len: int = 2048, dropout: float = 0.1):
        super().__init__()

        self.d_model = d_model
        self.max_layers = max_layers

        # Embeddings z Flow mixing
        self.token_embedding = nn.Embedding(vocab_size, d_model)
        self.pos_embedding = nn.Embedding(max_seq_len, d_model)

        # Flow embedding mixer - hierarchiczny
        self.embedding_flow = HierarchicalFlowMatrix(d_model * 2, d_model)

        # Dynamiczna topologia
        self.dynamic_topology = DynamicFlowTopology(d_model, max_layers, min_layers)

        # Output head z adaptive pooling
        self.output_pooling = nn.AdaptiveAvgPool1d(1)
        self.output_flow = MemoryEfficientFlowLayer(d_model, vocab_size)

        # Global flow controller z learnable scheduling
        self.global_flow_gate = nn.Parameter(torch.ones(1))

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, input_ids: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, List[Dict]]:
        batch_size, seq_len = input_ids.shape
        device = input_ids.device

        # Embeddings
        positions = torch.arange(0, seq_len, device=device).unsqueeze(0).expand(batch_size, -1)

        token_emb = self.token_embedding(input_ids)
        pos_emb = self.pos_embedding(positions)

        # Hierarchiczne łączenie embeddings
        combined_emb = torch.cat([token_emb, pos_emb], dim=-1)
        x, emb_metrics = self.embedding_flow(combined_emb)
        x = self.dropout(x)

        # Global flow gate - proste kontrolowanie przepływu
        x = x * self.global_flow_gate

        # Przepływ przez dynamiczną topologię
        x, topology_metrics = self.dynamic_topology(x)

        # Output projection
        logits, output_metrics = self.output_flow(x)

        # Zbierz wszystkie metryki
        all_metrics = [emb_metrics] + topology_metrics + [output_metrics]

        # Dodaj globalne metryki
        global_metrics = {
            'global_flow_gate': self.global_flow_gate.item(),
            'sequence_length': seq_len,
            'effective_computation': len(topology_metrics) - 1  # -1 bo ostatni to topology_metrics
        }
        all_metrics.append(global_metrics)

        return logits, all_metrics

class UnifiedFlowLoss(nn.Module):
    """
    Zunifikowana funkcja straty - implementacja sugestii Claude'a
    Używa tylko PyTorch tensors i learnable weights
    """

    def __init__(self, base_weight: float = 1.0):
        super().__init__()
        self.base_weight = base_weight

        # Learnable loss weights - będą się uczyć optymalnych proporcji
        self.loss_weights = nn.Parameter(torch.tensor([1.0, 0.1, 0.01, 0.001]))

    def _extract_tensor_metrics(self, metrics_list: List[Dict]) -> Dict[str, torch.Tensor]:
        """Wyciągnij metryki jako tensory PyTorch"""
        sparsity_tensors = []
        diversity_tensors = []
        efficiency_tensors = []
        stability_tensors = []

        for metrics in metrics_list:
            for key, value in metrics.items():
                # Konwertuj wszystko na tensory
                if isinstance(value, (int, float)):
                    value = torch.tensor(value, dtype=torch.float32)
                elif isinstance(value, torch.Tensor):
                    if value.numel() > 1:
                        value = value.mean()
                    value = value.float()
                else:
                    continue

                # Kategoryzuj metryki
                if 'sparsity' in key or 'active_ratio' in key:
                    sparsity_tensors.append(value)
                elif 'diversity' in key or 'entropy' in key:
                    diversity_tensors.append(value)
                elif 'efficiency' in key:
                    efficiency_tensors.append(value)
                elif 'balance' in key or 'schedule' in key:
                    stability_tensors.append(value)

        # Agreguj tensory
        result = {}
        if sparsity_tensors:
            result['sparsity'] = torch.stack(sparsity_tensors).mean()
        if diversity_tensors:
            result['diversity'] = torch.stack(diversity_tensors).mean()
        if efficiency_tensors:
            result['efficiency'] = torch.stack(efficiency_tensors).mean()
        if stability_tensors:
            result['stability'] = torch.stack(stability_tensors).var()

        return result

    def forward(self, logits: torch.Tensor, targets: torch.Tensor,
                metrics_list: List[Dict]) -> Tuple[torch.Tensor, Dict]:

        # Task loss
        task_loss = F.cross_entropy(
            logits.view(-1, logits.size(-1)),
            targets.view(-1),
            ignore_index=-100
        )

        # Wyciągnij metryki jako tensory
        tensor_metrics = self._extract_tensor_metrics(metrics_list)

        # Oblicz regularizatory (wszystko w PyTorch)
        sparsity_reg = tensor_metrics.get('sparsity', torch.tensor(0.0))
        diversity_reg = -tensor_metrics.get('diversity', torch.tensor(0.0))  # Maksymalizuj
        efficiency_reg = -tensor_metrics.get('efficiency', torch.tensor(0.0))  # Maksymalizuj
        stability_reg = tensor_metrics.get('stability', torch.tensor(0.0))  # Minimalizuj wariancję

        # Learnable weighted combination z softmax normalization
        weights = F.softmax(self.loss_weights, dim=0)

        # Przenieś wszystko na to samo urządzenie co task_loss
        device = task_loss.device
        sparsity_reg = sparsity_reg.to(device)
        diversity_reg = diversity_reg.to(device)
        efficiency_reg = efficiency_reg.to(device)
        stability_reg = stability_reg.to(device)

        total_loss = (weights[0] * task_loss +
                     weights[1] * sparsity_reg +
                     weights[2] * diversity_reg +
                     weights[3] * stability_reg)

        loss_info = {
            'total': total_loss.item(),
            'task': task_loss.item(),
            'sparsity': sparsity_reg.item(),
            'diversity': diversity_reg.item(),
            'efficiency': efficiency_reg.item(),
            'stability': stability_reg.item(),
            'weights': weights.detach().cpu().numpy().tolist()
        }

        return total_loss, loss_info

def analyze_flow_network_v2(model: FlowNetworkV2, input_ids: torch.Tensor) -> Dict:
    """Zaawansowana analiza sieci Flow V2"""
    model.eval()

    with torch.no_grad():
        logits, metrics_list = model(input_ids)

    # Podstawowe statystyki
    total_params = sum(p.numel() for p in model.parameters())

    # Analiza metryk
    analysis = {
        'total_parameters': total_params,
        'model_size_mb': total_params * 4 / (1024**2),
        'sequence_length': input_ids.shape[1],
        'batch_size': input_ids.shape[0]
    }

    # Zbierz szczegółowe metryki
    for i, metrics in enumerate(metrics_list):
        for key, value in metrics.items():
            if isinstance(value, torch.Tensor):
                value = value.item() if value.numel() == 1 else value.mean().item()
            analysis[f'layer_{i}_{key}'] = value

    # Oblicz efektywność
    active_layers = [m.get('active_layers', 0) for m in metrics_list if 'active_layers' in m]
    if active_layers:
        analysis['avg_active_layers'] = np.mean(active_layers)
        analysis['computational_efficiency'] = np.mean(active_layers) / model.max_layers

    return analysis

def benchmark_flow_networks(vocab_size: int = 1000, d_model: int = 256, seq_len: int = 64,
                           batch_size: int = 4, device: str = 'cpu'):
    """Porównanie Flow V1 vs V2"""
    print("🔥 Flow Networks Benchmark")
    print("=" * 50)

    # Import oryginalnego modelu
    from flow_module import FlowNetwork, analyze_flow_network

    # Stwórz oba modele
    print("Creating models...")
    model_v1 = FlowNetwork(vocab_size, d_model, num_layers=3, sparsity_ratio=0.2)
    model_v2 = FlowNetworkV2(vocab_size, d_model, max_layers=6, min_layers=3)

    # Przenieś na device
    model_v1 = model_v1.to(device)
    model_v2 = model_v2.to(device)

    # Test data
    input_ids = torch.randint(1, vocab_size, (batch_size, seq_len)).to(device)

    print(f"\n📊 Model Comparison:")
    print(f"Input shape: {input_ids.shape}")

    # Analiza V1
    print("\n🔹 Flow Network V1:")
    analysis_v1 = analyze_flow_network(model_v1, input_ids)
    for key, value in analysis_v1.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.4f}")
        else:
            print(f"  {key}: {value:,}")

    # Analiza V2
    print("\n🔹 Flow Network V2:")
    analysis_v2 = analyze_flow_network_v2(model_v2, input_ids)

    # Pokaż tylko kluczowe metryki
    key_metrics = ['total_parameters', 'model_size_mb', 'avg_active_layers', 'computational_efficiency']
    for key in key_metrics:
        if key in analysis_v2:
            value = analysis_v2[key]
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value:,}")

    # Porównanie wydajności
    print(f"\n📈 Performance Comparison:")
    print(f"  V1 Parameters: {analysis_v1['total_parameters']:,}")
    print(f"  V2 Parameters: {analysis_v2['total_parameters']:,}")

    if 'computational_efficiency' in analysis_v2:
        print(f"  V2 Computational Efficiency: {analysis_v2['computational_efficiency']:.2%}")

    # Memory efficiency
    v1_memory = analysis_v1['model_size_mb']
    v2_memory = analysis_v2['model_size_mb']
    print(f"  Memory V1: {v1_memory:.1f} MB")
    print(f"  Memory V2: {v2_memory:.1f} MB")

    return model_v1, model_v2, analysis_v1, analysis_v2

def train_flow_network_v2(model, data, num_epochs: int = 1, lr: float = 1e-3):
    """Trening Flow Network V2 z zunifikowaną funkcją straty"""
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=0.01)
    loss_fn = UnifiedFlowLoss(base_weight=1.0)

    model.train()

    for epoch in range(num_epochs):
        total_loss = 0
        num_batches = len(data)

        for batch_idx, (input_ids, targets) in enumerate(data):
            optimizer.zero_grad()

            # Forward
            logits, metrics = model(input_ids)
            loss, loss_info = loss_fn(logits, targets, metrics)

            # Backward
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            total_loss += loss.item()

            if batch_idx % 3 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx+1}/{num_batches}")
                print(f"  Loss: {loss.item():.4f}")
                for key, value in loss_info.items():
                    print(f"  {key}: {value:.6f}")

        print(f"Epoch {epoch+1} completed. Average loss: {total_loss/num_batches:.4f}\n")

def create_dummy_data(vocab_size: int, seq_len: int, batch_size: int, num_batches: int = 10):
    """Stwórz dummy data dla testów"""
    data = []
    for _ in range(num_batches):
        input_ids = torch.randint(1, vocab_size, (batch_size, seq_len))
        targets = torch.randint(1, vocab_size, (batch_size, seq_len))
        data.append((input_ids, targets))
    return data

if __name__ == "__main__":
    print("🚀 Flow Neural Network V2 - Advanced Test")
    print("=" * 60)

    # Konfiguracja
    vocab_size = 1000
    d_model = 256
    seq_len = 64
    batch_size = 4
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    print(f"Device: {device}")

    # Benchmark porównawczy
    try:
        model_v1, model_v2, analysis_v1, analysis_v2 = benchmark_flow_networks(
            vocab_size, d_model, seq_len, batch_size, device
        )
        print("✓ Benchmark completed successfully!")

    except Exception as e:
        print(f"✗ Benchmark error: {e}")
        # Fallback - test tylko V2
        print("\nTesting V2 only...")
        model_v2 = FlowNetworkV2(vocab_size, d_model, max_layers=6, min_layers=3)
        input_ids = torch.randint(1, vocab_size, (batch_size, seq_len))

        analysis_v2 = analyze_flow_network_v2(model_v2, input_ids)
        print("V2 Analysis:")
        for key, value in list(analysis_v2.items())[:10]:  # Pokaż pierwsze 10
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")

    # Test treningu V2
    print("\n🚀 Testing V2 Training...")
    try:
        dummy_data = create_dummy_data(vocab_size, seq_len, batch_size, num_batches=3)
        train_flow_network_v2(model_v2, dummy_data, num_epochs=1, lr=1e-3)
        print("✓ V2 Training completed successfully!")

    except Exception as e:
        print(f"✗ V2 Training error: {e}")
        import traceback
        traceback.print_exc()

    print("\n🎉 Flow Network V2 test completed!")
